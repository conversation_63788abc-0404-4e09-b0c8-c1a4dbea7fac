# Enhanced MongoDB Document Structure

## Overview

This document describes the enhanced MongoDB document structure implemented in the space-mongo-documents module. The enhancement converts simple array/object structures into proper nested embedded documents using Doctrine ODM, providing better type safety, validation, and business logic encapsulation while maintaining full backward compatibility.

## Field Order Optimization

The document structure has been reorganized to match the actual MongoDB field order as observed in the database. This ensures consistency between the PHP document classes and the actual stored data structure.

### MongoDB Field Order Analysis

Based on actual database queries and API responses, the Vehicle document fields are ordered as follows:

1. **vin** - Primary identifier (always first)
2. **brand** - Vehicle brand
3. **model** - Vehicle model
4. **version** - Vehicle version
5. **versionId** - Version identifier
6. **registrationNumber** - Registration number
7. **registrationDate** - Registration date
8. **color** - Vehicle color
9. **energy** - Energy type (Electric, ICE, etc.)
10. **status** - Vehicle status
11. **featureCode** - Feature codes array (backward compatibility)

This order matches the structure seen in actual MongoDB documents and API responses from the test endpoint.

## Enhanced Document Classes

### 1. FeatureCode Document

**Purpose**: Represents vehicle feature codes with proper structure and validation.

**Fields**:
- `code`: String - Feature code identifier (e.g., 'UBI_PHYD', 'VEHICLE_INFO')
- `status`: String - Status ('enable', 'disable', 'enabled', 'disabled')
- `value`: String - Feature value (e.g., 'NAE01')
- `config`: Array - Configuration data
- `calcTimestamp`: Integer - Calculation timestamp

**Key Methods**:
- `isEnabled()`: Check if feature is enabled
- `isDisabled()`: Check if feature is disabled
- `toArray()`: Convert to array for backward compatibility
- `fromArray()`: Create from array data

### 2. ScoreCategory Document

**Purpose**: Represents driving score category breakdowns (dynamics, deceleration, cornering).

**Fields**:
- `percentageOfGood`: Float - Percentage of good driving behavior
- `percentageOfAverage`: Float - Percentage of average driving behavior
- `percentageOfBad`: Float - Percentage of bad driving behavior
- `tips`: String - Driving improvement tips

**Key Methods**:
- `getTotalPercentage()`: Get sum of all percentages
- `isValid()`: Check if percentages are valid (sum ≈ 100%)

### 3. SubScoreData Document

**Purpose**: Contains detailed score breakdowns for driving behavior analysis.

**Fields**:
- `dynamics`: ScoreCategory - Acceleration/dynamics scores
- `deceleration`: ScoreCategory - Braking/deceleration scores
- `cornering`: ScoreCategory - Cornering behavior scores

**Key Methods**:
- `getOverallQuality()`: Get overall driving quality assessment
- `toArray()`: Maintains API format with 'dynamincs'/'decelaration' typos for compatibility

### 4. ScoreData Document

**Purpose**: Represents overall and daily driving scores with optional detailed breakdowns.

**Fields**:
- `value`: Float - Score value (0-100)
- `subScore`: SubScoreData - Detailed score breakdown (optional)

**Key Methods**:
- `getValueAsInt()`: Get score as integer
- `getValueAsString()`: Get formatted score string
- `getCategory()`: Get score category ('excellent', 'good', 'average', 'poor')
- `hasSubScore()`: Check if detailed breakdown is available

### 5. MileageData Document

**Purpose**: Represents vehicle mileage information with date tracking.

**Fields**:
- `value`: Integer - Mileage value
- `date`: DateTime - Date of mileage reading
- `timestamp`: Integer - Unix timestamp
- `unit`: String - Unit ('km', 'miles')

**Key Methods**:
- `getValueInMiles()`: Convert to miles
- `getValueInKm()`: Convert to kilometers
- `isRecent()`: Check if reading is within last 30 days

## Enhanced Main Documents

### Vehicle Document Enhancements

**New Fields**:
- `featureCodes`: Collection<FeatureCode> - Embedded feature code objects
- `mileage`: MileageData - Embedded mileage data

**Enhanced Methods**:
- `getFeatureCodes()`: Get feature codes as objects
- `addFeatureCodeObject()`: Add feature code object
- `findFeatureCodeObject()`: Find feature code by code
- `isFeatureCodeEnabled()`: Check if specific feature is enabled
- `getEnabledFeatureCodes()`: Get all enabled features

**Backward Compatibility**:
- Original `featureCode` array field maintained
- Automatic synchronization between array and embedded documents
- `syncFeatureCodesFromArray()` / `syncFeatureCodesToArray()` methods

### DrivingScore Document Enhancements

**New Fields**:
- `overallScoreData`: ScoreData - Enhanced overall score
- `dailyScoreData`: ScoreData - Enhanced daily score with breakdowns

**Enhanced Methods**:
- `getOverallScoreData()` / `getDailyScoreData()`: Get score objects
- `getDrivingQuality()`: Get overall quality assessment
- `hasDetailedBreakdown()`: Check for detailed score analysis

**Backward Compatibility**:
- Original `overallScore` and `dailyScore` arrays maintained
- Automatic synchronization between arrays and embedded documents

## Backward Compatibility Strategy

### 1. Dual Storage Approach
- Both old array format and new embedded documents are stored
- Automatic synchronization ensures data consistency
- Existing APIs continue to work without modification

### 2. Synchronization Methods
- `prePersist()`: Sync embedded documents to arrays before saving
- `postLoad()`: Sync arrays to embedded documents after loading
- Manual sync methods available for explicit control

### 3. Migration Support
- `fromArray()` static methods for creating objects from existing data
- `toArray()` methods for converting objects back to arrays
- Gradual migration path without breaking changes

## Usage Examples

### Working with Enhanced Feature Codes

```php
// Create new feature code
$featureCode = new FeatureCode();
$featureCode->setCode('UBI_PHYD')
           ->setStatus('enable')
           ->setValue('NAE01')
           ->setConfig(['engine' => 'BEV']);

// Add to vehicle
$vehicle->addFeatureCodeObject($featureCode);

// Check if feature is enabled
if ($vehicle->isFeatureCodeEnabled('UBI_PHYD')) {
    // Feature is enabled
}

// Get all enabled features
$enabledFeatures = $vehicle->getEnabledFeatureCodes();
```

### Working with Enhanced Driving Scores

```php
// Create detailed score data
$dynamics = new ScoreCategory();
$dynamics->setPercentageOfGood(75.0)
         ->setPercentageOfAverage(15.0)
         ->setPercentageOfBad(10.0)
         ->setTips('Maintain smooth acceleration');

$subScore = new SubScoreData();
$subScore->setDynamics($dynamics);

$dailyScore = new ScoreData();
$dailyScore->setValue(78.5)
           ->setSubScore($subScore);

$drivingScore->setDailyScoreData($dailyScore);

// Get driving quality assessment
$quality = $drivingScore->getDrivingQuality(); // 'good'
```

## Benefits

### 1. Type Safety
- Strong typing for all nested data structures
- IDE autocompletion and type hints
- Reduced runtime errors

### 2. Business Logic Encapsulation
- Methods for common operations (isEnabled, getCategory, etc.)
- Validation logic within document classes
- Consistent behavior across the application

### 3. Maintainability
- Clear separation of concerns
- Easier to extend and modify
- Better code organization

### 4. API Consistency
- Maintains existing API contracts
- Gradual migration path
- No breaking changes for existing consumers

### 5. Enhanced Functionality
- Rich query capabilities with embedded documents
- Better data validation
- Improved performance for complex operations

## Migration Path

### Phase 1: Enhanced Structure (Current)
- New embedded document classes created
- Dual storage with synchronization
- Backward compatibility maintained

### Phase 2: Gradual Adoption
- Microservices gradually adopt enhanced methods
- Testing with both old and new approaches
- Performance monitoring and optimization

### Phase 3: Full Migration
- Complete transition to embedded documents
- Remove legacy array fields (optional)
- Optimize for new structure

## Testing

Comprehensive test coverage includes:
- Unit tests for all embedded document classes
- Integration tests for synchronization
- Backward compatibility verification
- Performance benchmarking

See `tests/Document/EnhancedDocumentTest.php` for detailed test examples.

## Dealer Document Structure

### Overview

The dealer document structure provides a comprehensive, type-safe approach to managing dealer data across all microservices. It replaces the previous array-based dealer models with proper embedded document relationships while maintaining full backward compatibility.

### Dealer Document Classes

#### 1. Dealer Document (Main)

**Purpose**: Main dealer document containing all dealer information with embedded sub-documents.

**Fields** (MongoDB field order):
- `dealerId`: String - Unique dealer identifier
- `name`: String - Dealer name
- `brand`: String - Vehicle brand (Peugeot, Citroën, etc.)
- `market`: String - Market code (FR, DE, etc.)
- `status`: String - Dealer status (active, inactive)

**Embedded Documents**:
- `address`: DealerAddress - Physical address information
- `coordinate`: DealerCoordinate - GPS coordinates
- `contact`: DealerContact - Contact information (phones, emails)
- `business`: DealerBusiness - Business type, hours, indicators
- `website`: DealerWebsite - Website URLs and pages
- `codes`: DealerCodes - Dealer codes and identifiers

**Key Methods**:
- `isActive()`: Check if dealer is active
- `isCurrentlyOpen()`: Check if dealer is currently open
- `distanceToPoint()`: Calculate distance to coordinates
- `getPrimaryPhone()`: Get main contact phone
- `getPrimaryEmail()`: Get main contact email
- `isComplete()`: Check if dealer has complete information

#### 2. DealerAddress Document

**Purpose**: Physical address information with validation and formatting.

**Fields**:
- `city`: String - City name
- `country`: String - Country code/name
- `line1`: String - Address line 1 (street)
- `line2`: String - Address line 2 (optional)
- `line3`: String - Address line 3 (optional)
- `region`: String - Region/state
- `zipCode`: String - Postal code

**Key Methods**:
- `getFormattedAddress()`: Get complete formatted address
- `isComplete()`: Check if address has required fields
- `getStreetAddress()`: Get combined street lines

#### 3. DealerCoordinate Document

**Purpose**: GPS coordinates with distance calculation capabilities.

**Fields**:
- `latitude`: Float - Latitude coordinate
- `longitude`: Float - Longitude coordinate

**Key Methods**:
- `distanceTo()`: Calculate distance to another coordinate
- `distanceToPoint()`: Calculate distance to lat/lng point
- `isValid()`: Validate coordinate ranges
- `isWithinBounds()`: Check if within bounding box

#### 4. DealerContact Document

**Purpose**: Comprehensive contact information for different departments.

**Fields**:
- Phone numbers: `phoneNumber`, `phoneApv`, `phonePr`, `phoneVn`, `phoneVo`
- Email addresses: `email`, `emailApv`, `emailAgent`, `emailGer`, `emailGrc`, `emailPr`, `emailSales`, `emailVo`

**Key Methods**:
- `getPrimaryPhone()`: Get main phone number
- `getPrimaryEmail()`: Get main email address
- `getAllPhones()`: Get all available phone numbers
- `getAllEmails()`: Get all available email addresses
- `hasContactInfo()`: Check if contact info is available

#### 5. DealerBusiness Document

**Purpose**: Business information including type, hours, and indicators.

**Fields**:
- `businessType`: String - Business type code
- `businessTypeCode`: String - Business type identifier
- `businessTypeLabel`: String - Business type display name
- `openingHours`: Array - Operating hours by day
- `indicators`: Array - Business indicators/features

**Key Methods**:
- `isOpenOnDay()`: Check if open on specific day
- `isCurrentlyOpen()`: Check if currently open
- `hasIndicator()`: Check for specific indicator
- `getOpeningHoursForDay()`: Get hours for specific day

#### 6. DealerWebsite Document

**Purpose**: Website URLs and online presence management.

**Fields**:
- `url`: String - Main website URL
- `urlApv`: String - Service website URL
- `urlPr`: String - Parts website URL
- `urlVn`: String - New vehicles URL
- `urlVo`: String - Used vehicles URL
- `urlPages`: Array - Additional website pages

**Key Methods**:
- `getPrimaryUrl()`: Get main website URL
- `getUrlByType()`: Get URL for specific type
- `validateUrls()`: Validate all URL formats
- `hasWebsiteInfo()`: Check if website info available

#### 7. DealerCodes Document

**Purpose**: Dealer identification codes and hierarchy information.

**Fields**:
- `codeActeur`: String - Actor code
- `codeActeurPrincipal`: String - Principal actor code
- `codeNature`: String - Nature code
- `dealerCode`: String - Main dealer code
- `idSite`: String - Site identifier
- `mainDealerCode`: String - Main dealer reference
- `market`: String - Market code
- `oic`: String - OIC code
- `outlet`: String - Outlet code

**Key Methods**:
- `isMainDealer()`: Check if this is a main dealer
- `getHierarchyLevel()`: Get dealer hierarchy level
- `getMarketDealerCode()`: Get market-specific code
- `belongsToMarket()`: Check market membership

### Integration with Existing APIs

#### Backward Compatibility Strategy

1. **Dual Storage**: Both embedded documents and legacy arrays maintained
2. **Automatic Sync**: `prePersist()` and `postLoad()` methods ensure consistency
3. **API Preservation**: All existing dealer APIs continue to work unchanged
4. **Migration Support**: `fromArray()` and `toArray()` methods for seamless conversion

#### Repository Features

The `DealerRepository` provides advanced querying capabilities:

- **Geographic Queries**: `findNearLocation()` for proximity searches
- **Business Queries**: `findByBusinessType()`, `findWithIndicator()`
- **Search Functions**: `search()` for name/ID searches
- **Statistics**: `getStatistics()` for dealer analytics
- **Hierarchy Queries**: `findMainDealers()`, `findSubsidiaries()`

### Usage Examples

#### Creating a Complete Dealer

```php
$dealer = new Dealer();
$dealer->setDealerId('FR001')
       ->setName('Dealer Paris Centre')
       ->setBrand('Peugeot')
       ->setMarket('FR')
       ->setStatus('active');

// Set address
$address = new DealerAddress();
$address->setCity('Paris')
        ->setCountry('France')
        ->setLine1('123 Rue de la Paix');
$dealer->setAddress($address);

// Set coordinates
$coordinate = new DealerCoordinate();
$coordinate->setLatitude(48.8566)->setLongitude(2.3522);
$dealer->setCoordinate($coordinate);

// Set contact information
$contact = new DealerContact();
$contact->setPhoneNumber('+33 1 23 45 67 89')
        ->setEmail('<EMAIL>');
$dealer->setContact($contact);
```

#### Finding Nearby Dealers

```php
$repository = $documentManager->getRepository(Dealer::class);
$nearbyDealers = $repository->findNearLocation(48.8566, 2.3522, 25); // 25km radius

foreach ($nearbyDealers as $dealer) {
    $distance = $dealer->distanceToPoint(48.8566, 2.3522);
    echo $dealer->getName() . " - " . round($distance, 2) . "km away\n";
}
```

#### Business Logic Operations

```php
// Check if dealer is currently open
if ($dealer->isCurrentlyOpen()) {
    echo "Dealer is currently open!";
}

// Get contact information
$phone = $dealer->getPrimaryPhone();
$email = $dealer->getPrimaryEmail();
$website = $dealer->getPrimaryWebsite();

// Check dealer completeness
if ($dealer->isComplete()) {
    echo "Dealer has complete information";
}
```

### Benefits of Enhanced Dealer Structure

1. **Type Safety**: Strong typing for all dealer data structures
2. **Business Logic**: Rich methods for common dealer operations
3. **Geographic Capabilities**: Built-in distance calculations and proximity searches
4. **Validation**: Comprehensive validation for addresses, coordinates, URLs
5. **Hierarchy Support**: Main dealer and subsidiary relationships
6. **API Compatibility**: Seamless integration with existing dealer APIs
7. **Performance**: Optimized MongoDB queries with proper indexing support

### Testing

Comprehensive test coverage includes:
- Unit tests for all dealer document classes
- Integration tests with existing dealer APIs
- Geographic calculation accuracy tests
- Backward compatibility verification

See `tests/Document/DealerDocumentTest.php` for detailed test examples.
