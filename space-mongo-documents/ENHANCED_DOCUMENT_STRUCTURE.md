# Enhanced MongoDB Document Structure

## Overview

This document describes the enhanced MongoDB document structure implemented in the space-mongo-documents module. The enhancement converts simple array/object structures into proper nested embedded documents using Doctrine ODM, providing better type safety, validation, and business logic encapsulation while maintaining full backward compatibility.

## Field Order Optimization

The document structure has been reorganized to match the actual MongoDB field order as observed in the database. This ensures consistency between the PHP document classes and the actual stored data structure.

### MongoDB Field Order Analysis

Based on actual database queries and API responses, the Vehicle document fields are ordered as follows:

1. **vin** - Primary identifier (always first)
2. **brand** - Vehicle brand
3. **model** - Vehicle model
4. **version** - Vehicle version
5. **versionId** - Version identifier
6. **registrationNumber** - Registration number
7. **registrationDate** - Registration date
8. **color** - Vehicle color
9. **energy** - Energy type (Electric, ICE, etc.)
10. **status** - Vehicle status
11. **featureCode** - Feature codes array (backward compatibility)

This order matches the structure seen in actual MongoDB documents and API responses from the test endpoint.

## Enhanced Document Classes

### 1. FeatureCode Document

**Purpose**: Represents vehicle feature codes with proper structure and validation.

**Fields**:
- `code`: String - Feature code identifier (e.g., 'UBI_PHYD', 'VEHICLE_INFO')
- `status`: String - Status ('enable', 'disable', 'enabled', 'disabled')
- `value`: String - Feature value (e.g., 'NAE01')
- `config`: Array - Configuration data
- `calcTimestamp`: Integer - Calculation timestamp

**Key Methods**:
- `isEnabled()`: Check if feature is enabled
- `isDisabled()`: Check if feature is disabled
- `toArray()`: Convert to array for backward compatibility
- `fromArray()`: Create from array data

### 2. ScoreCategory Document

**Purpose**: Represents driving score category breakdowns (dynamics, deceleration, cornering).

**Fields**:
- `percentageOfGood`: Float - Percentage of good driving behavior
- `percentageOfAverage`: Float - Percentage of average driving behavior
- `percentageOfBad`: Float - Percentage of bad driving behavior
- `tips`: String - Driving improvement tips

**Key Methods**:
- `getTotalPercentage()`: Get sum of all percentages
- `isValid()`: Check if percentages are valid (sum ≈ 100%)

### 3. SubScoreData Document

**Purpose**: Contains detailed score breakdowns for driving behavior analysis.

**Fields**:
- `dynamics`: ScoreCategory - Acceleration/dynamics scores
- `deceleration`: ScoreCategory - Braking/deceleration scores
- `cornering`: ScoreCategory - Cornering behavior scores

**Key Methods**:
- `getOverallQuality()`: Get overall driving quality assessment
- `toArray()`: Maintains API format with 'dynamincs'/'decelaration' typos for compatibility

### 4. ScoreData Document

**Purpose**: Represents overall and daily driving scores with optional detailed breakdowns.

**Fields**:
- `value`: Float - Score value (0-100)
- `subScore`: SubScoreData - Detailed score breakdown (optional)

**Key Methods**:
- `getValueAsInt()`: Get score as integer
- `getValueAsString()`: Get formatted score string
- `getCategory()`: Get score category ('excellent', 'good', 'average', 'poor')
- `hasSubScore()`: Check if detailed breakdown is available

### 5. MileageData Document

**Purpose**: Represents vehicle mileage information with date tracking.

**Fields**:
- `value`: Integer - Mileage value
- `date`: DateTime - Date of mileage reading
- `timestamp`: Integer - Unix timestamp
- `unit`: String - Unit ('km', 'miles')

**Key Methods**:
- `getValueInMiles()`: Convert to miles
- `getValueInKm()`: Convert to kilometers
- `isRecent()`: Check if reading is within last 30 days

## Enhanced Main Documents

### Vehicle Document Enhancements

**New Fields**:
- `featureCodes`: Collection<FeatureCode> - Embedded feature code objects
- `mileage`: MileageData - Embedded mileage data

**Enhanced Methods**:
- `getFeatureCodes()`: Get feature codes as objects
- `addFeatureCodeObject()`: Add feature code object
- `findFeatureCodeObject()`: Find feature code by code
- `isFeatureCodeEnabled()`: Check if specific feature is enabled
- `getEnabledFeatureCodes()`: Get all enabled features

**Backward Compatibility**:
- Original `featureCode` array field maintained
- Automatic synchronization between array and embedded documents
- `syncFeatureCodesFromArray()` / `syncFeatureCodesToArray()` methods

### DrivingScore Document Enhancements

**New Fields**:
- `overallScoreData`: ScoreData - Enhanced overall score
- `dailyScoreData`: ScoreData - Enhanced daily score with breakdowns

**Enhanced Methods**:
- `getOverallScoreData()` / `getDailyScoreData()`: Get score objects
- `getDrivingQuality()`: Get overall quality assessment
- `hasDetailedBreakdown()`: Check for detailed score analysis

**Backward Compatibility**:
- Original `overallScore` and `dailyScore` arrays maintained
- Automatic synchronization between arrays and embedded documents

## Backward Compatibility Strategy

### 1. Dual Storage Approach
- Both old array format and new embedded documents are stored
- Automatic synchronization ensures data consistency
- Existing APIs continue to work without modification

### 2. Synchronization Methods
- `prePersist()`: Sync embedded documents to arrays before saving
- `postLoad()`: Sync arrays to embedded documents after loading
- Manual sync methods available for explicit control

### 3. Migration Support
- `fromArray()` static methods for creating objects from existing data
- `toArray()` methods for converting objects back to arrays
- Gradual migration path without breaking changes

## Usage Examples

### Working with Enhanced Feature Codes

```php
// Create new feature code
$featureCode = new FeatureCode();
$featureCode->setCode('UBI_PHYD')
           ->setStatus('enable')
           ->setValue('NAE01')
           ->setConfig(['engine' => 'BEV']);

// Add to vehicle
$vehicle->addFeatureCodeObject($featureCode);

// Check if feature is enabled
if ($vehicle->isFeatureCodeEnabled('UBI_PHYD')) {
    // Feature is enabled
}

// Get all enabled features
$enabledFeatures = $vehicle->getEnabledFeatureCodes();
```

### Working with Enhanced Driving Scores

```php
// Create detailed score data
$dynamics = new ScoreCategory();
$dynamics->setPercentageOfGood(75.0)
         ->setPercentageOfAverage(15.0)
         ->setPercentageOfBad(10.0)
         ->setTips('Maintain smooth acceleration');

$subScore = new SubScoreData();
$subScore->setDynamics($dynamics);

$dailyScore = new ScoreData();
$dailyScore->setValue(78.5)
           ->setSubScore($subScore);

$drivingScore->setDailyScoreData($dailyScore);

// Get driving quality assessment
$quality = $drivingScore->getDrivingQuality(); // 'good'
```

## Benefits

### 1. Type Safety
- Strong typing for all nested data structures
- IDE autocompletion and type hints
- Reduced runtime errors

### 2. Business Logic Encapsulation
- Methods for common operations (isEnabled, getCategory, etc.)
- Validation logic within document classes
- Consistent behavior across the application

### 3. Maintainability
- Clear separation of concerns
- Easier to extend and modify
- Better code organization

### 4. API Consistency
- Maintains existing API contracts
- Gradual migration path
- No breaking changes for existing consumers

### 5. Enhanced Functionality
- Rich query capabilities with embedded documents
- Better data validation
- Improved performance for complex operations

## Migration Path

### Phase 1: Enhanced Structure (Current)
- New embedded document classes created
- Dual storage with synchronization
- Backward compatibility maintained

### Phase 2: Gradual Adoption
- Microservices gradually adopt enhanced methods
- Testing with both old and new approaches
- Performance monitoring and optimization

### Phase 3: Full Migration
- Complete transition to embedded documents
- Remove legacy array fields (optional)
- Optimize for new structure

## Testing

Comprehensive test coverage includes:
- Unit tests for all embedded document classes
- Integration tests for synchronization
- Backward compatibility verification
- Performance benchmarking

See `tests/Document/EnhancedDocumentTest.php` for detailed test examples.
