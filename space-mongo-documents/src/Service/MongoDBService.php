<?php

namespace Space\MongoDocuments\Service;

use Doctrine\ODM\MongoDB\DocumentManager;
use Psr\Log\LoggerInterface;
use Space\MongoDocuments\Document\Settings;

class MongoDBService
{
    public function __construct(
        private DocumentManager $documentManager,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Find a document by ID
     */
    public function find(string $documentClass, string $id)
    {
        try {
            return $this->documentManager->find($documentClass, $id);
        } catch (\Exception $e) {
            $this->logger->error('Error finding document', [
                'class' => $documentClass,
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Find documents by criteria
     */
    public function findBy(string $documentClass, array $criteria, array $orderBy = null, $limit = null, $offset = null)
    {
        $this->logger->info('MongoDBService: Starting findBy query', [
            'document_class' => $documentClass,
            'criteria' => $criteria,
            'orderBy' => $orderBy,
            'limit' => $limit,
            'offset' => $offset
        ]);

        try {
            $result = $this->documentManager->getRepository($documentClass)->findBy($criteria, $orderBy, $limit, $offset);

            $this->logger->info('MongoDBService: findBy query completed', [
                'document_class' => $documentClass,
                'criteria' => $criteria,
                'result_count' => count($result)
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('MongoDBService: Error finding documents by criteria', [
                'class' => $documentClass,
                'criteria' => $criteria,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Find a single document by criteria
     */
    public function findOneBy(string $documentClass, array $criteria)
    {
        $this->logger->info('MongoDBService: Starting findOneBy query', [
            'document_class' => $documentClass,
            'criteria' => $criteria,
            'database' => $this->documentManager->getConfiguration()->getDefaultDB()
        ]);

        try {
            $repository = $this->documentManager->getRepository($documentClass);

            $this->logger->info('MongoDBService: Repository details', [
                'repository_class' => get_class($repository),
                'document_class' => $documentClass
            ]);

            // Log connection details
            $connection = $this->documentManager->getClient();
            $this->logger->info('MongoDBService: Connection details', [
                'connection_class' => get_class($connection),
                'database_name' => $this->documentManager->getConfiguration()->getDefaultDB()
            ]);

            $result = $repository->findOneBy($criteria);

            if ($result) {
                $this->logger->info('MongoDBService: Document found', [
                    'document_class' => $documentClass,
                    'criteria' => $criteria,
                    'result_id' => method_exists($result, 'getId') ? $result->getId() : 'no_id_method',
                    'result_class' => get_class($result)
                ]);
            } else {
                $this->logger->warning('MongoDBService: No document found', [
                    'document_class' => $documentClass,
                    'criteria' => $criteria
                ]);

                // Try to get collection stats for debugging
                try {
                    $collection = $this->documentManager->getDocumentCollection($documentClass);
                    $collectionName = $collection->getCollectionName();
                    $this->logger->info('MongoDBService: Collection info', [
                        'collection_name' => $collectionName,
                        'document_class' => $documentClass
                    ]);
                } catch (\Exception $collectionError) {
                    $this->logger->error('MongoDBService: Error getting collection info', [
                        'error' => $collectionError->getMessage()
                    ]);
                }
            }

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('MongoDBService: Error finding document by criteria', [
                'class' => $documentClass,
                'criteria' => $criteria,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return null;
        }
    }

    /**
     * Save a document
     */
    public function save($document, bool $flush = true): void
    {
        try {
            $this->documentManager->persist($document);

            if ($flush) {
                $this->documentManager->flush();
            }
        } catch (\Exception $e) {
            $this->logger->error('Error saving document', [
                'document' => get_class($document),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Update a document
     */
    public function update($document): void
    {
        try {
            $this->documentManager->flush();
        } catch (\Exception $e) {
            $this->logger->error('Error updating document', [
                'document' => get_class($document),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Remove a document
     */
    public function remove($document, bool $flush = true): void
    {
        try {
            $this->documentManager->remove($document);

            if ($flush) {
                $this->documentManager->flush();
            }
        } catch (\Exception $e) {
            $this->logger->error('Error removing document', [
                'document' => get_class($document),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Find settings by complex filter (supports MongoDB operators)
     */
    public function findSettingsByFilter(array $filter): ?Settings
    {
        try {
            $this->logger->info('MongoDBService: Starting findSettingsByFilter query', [
                'filter' => $filter
            ]);

            $queryBuilder = $this->documentManager->createQueryBuilder(Settings::class);

            // Handle basic field filters
            if (isset($filter['brand'])) {
                $queryBuilder->field('brand')->equals($filter['brand']);
            }
            if (isset($filter['source'])) {
                $queryBuilder->field('source')->equals($filter['source']);
            }
            if (isset($filter['culture'])) {
                $queryBuilder->field('culture')->equals($filter['culture']);
            }

            // Handle $or operator for nested settingsData queries
            if (isset($filter['$or']) && is_array($filter['$or'])) {
                $orConditions = [];
                foreach ($filter['$or'] as $orCondition) {
                    if (isset($orCondition['settingsData.o2x.code'])) {
                        $orConditions[] = ['settingsData.o2x.code' => $orCondition['settingsData.o2x.code']];
                    }
                    if (isset($orCondition['settingsData.config.code'])) {
                        $orConditions[] = ['settingsData.config.code' => $orCondition['settingsData.config.code']];
                    }
                }

                if (!empty($orConditions)) {
                    $queryBuilder->addOr($queryBuilder->expr()->field('settingsData.o2x.code')->exists(true));
                    $queryBuilder->addOr($queryBuilder->expr()->field('settingsData.config.code')->exists(true));
                }
            }

            $result = $queryBuilder->getQuery()->getSingleResult();

            if ($result) {
                $this->logger->info('MongoDBService: Settings found by filter', [
                    'filter' => $filter,
                    'result_id' => $result->getId()
                ]);
            } else {
                $this->logger->warning('MongoDBService: No settings found by filter', [
                    'filter' => $filter
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('MongoDBService: Error finding settings by filter', [
                'filter' => $filter,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Find settings by type, brand, and country
     */
    public function findSettingsByTypeBrandAndCountry(string $type, string $brand, string $country): ?Settings
    {
        try {
            return $this->documentManager->getRepository(Settings::class)->findOneBy([
                'type' => $type,
                'brand' => $brand,
                'country' => $country
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error finding settings by type, brand, and country', [
                'type' => $type,
                'brand' => $brand,
                'country' => $country,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get the document manager
     */
    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }
}
