# Testing Driving Score API

## Issue Analysis
The error "vehicle not found" occurs because the API is being called with VIN `VR3KCZKZ5RS101468` which doesn't belong to the user. 

From the logs, the user `6f5853d0f9e2442c9c818fecf0eec7cf` has these VINs:
- VR3UPHNKSKT101603
- 8ADUAFC67SG521741  
- VR3UKZKWZPJ792173
- VR3UHZKXZPT630364
- VR3F3DGYTMY513618

## Fixes Applied

### 1. Fixed DrivingScore Document
- Added `stliPolicyNumber` field
- Added `isValid` boolean field
- Added corresponding getters/setters

### 2. Fixed PHYDService
- Changed return type from `array` to `WSResponse`
- Added proper response formatting with `documents` array
- Added error handling with proper WSResponse

### 3. Added Repository Methods
- `findByVinAndStliPolicyNumber()`
- Enhanced existing methods

### 4. Created Test Endpoints
- `/test/user-vehicles/{userId}` - Get user's vehicles
- `/test/driving-scores?vin={vin}` - Get driving scores
- `/test/create-test-driving-score` - Create test data

## Testing Steps

### Step 1: Check User's Vehicles
```bash
curl -X GET "http://127.0.0.1:8000/test/user-vehicles/6f5853d0f9e2442c9c818fecf0eec7cf"
```

### Step 2: Create Test Driving Score Data
```bash
curl -X POST "http://127.0.0.1:8000/test/create-test-driving-score" \
  -H "Content-Type: application/json" \
  -d '{"vin": "VR3UPHNKSKT101603"}'
```

### Step 3: Test Driving Score API with Valid VIN
```bash
curl -X GET "http://127.0.0.1:8000/v1/vehicle/driving-score" \
  -H "userId: 6f5853d0f9e2442c9c818fecf0eec7cf" \
  -H "vin: VR3UPHNKSKT101603"
```

### Step 4: Check Driving Scores in Database
```bash
curl -X GET "http://127.0.0.1:8000/test/driving-scores?vin=VR3UPHNKSKT101603"
```

## Expected Results

### Before Fix
- Error: "Vehicle not found" when using wrong VIN
- Type errors in PHYDService return type

### After Fix
- Proper validation of VIN ownership
- Correct response format
- Working driving score retrieval
- Proper error handling

## API Usage

### Correct Usage
Use one of the user's actual VINs:
```bash
# Valid VINs for user 6f5853d0f9e2442c9c818fecf0eec7cf:
curl -H "userId: 6f5853d0f9e2442c9c818fecf0eec7cf" -H "vin: VR3UPHNKSKT101603" \
  http://127.0.0.1:8000/v1/vehicle/driving-score

curl -H "userId: 6f5853d0f9e2442c9c818fecf0eec7cf" -H "vin: 8ADUAFC67SG521741" \
  http://127.0.0.1:8000/v1/vehicle/driving-score
```

### Error Cases
```bash
# Wrong VIN (not owned by user)
curl -H "userId: 6f5853d0f9e2442c9c818fecf0eec7cf" -H "vin: VR3KCZKZ5RS101468" \
  http://127.0.0.1:8000/v1/vehicle/driving-score
# Expected: "No Vehicle found" error

# Missing headers
curl http://127.0.0.1:8000/v1/vehicle/driving-score
# Expected: Validation error
```

## Database Requirements

For the API to work properly, you need:

1. **User Data**: User with vehicles in `userData` collection
2. **Driving Score Data**: Corresponding records in `drivingScore` collection
3. **Feature Codes**: User vehicles should have `UBI_PHYD` feature code enabled

## Troubleshooting

### If still getting "vehicle not found":
1. Verify the VIN belongs to the user
2. Check user data in MongoDB
3. Use test endpoints to verify data

### If getting "Driving Score service not available":
1. Check if UBI_PHYD feature code exists and is enabled
2. Verify driving score data exists for the VIN
3. Check `isValid` field is true

### If getting type errors:
1. Ensure all document fields are properly mapped
2. Check MongoDB document structure matches code expectations
3. Verify return types in services match expectations
