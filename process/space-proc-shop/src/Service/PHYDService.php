<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\Serializer\SerializerInterface;
use Space\MongoDocuments\Document\DrivingScore;
use Space\MongoDocuments\Service\MongoDBService;


/**
 * PHYD service.
 */
class PHYDService
{
    use LoggerTrait;

    private VehicleModel $vehicleModel;

    public const COLLECTION = 'drivingScore';

    public const FEATURE_CODE_PHYD = 'UBI_PHYD';

    public const FEATURE_CODE_STATUS_ENABLE = 'enable';
    public const FEATURE_CODE_STATUS_DISABLE = 'disable';

    public function __construct(
        private MongoDBService $mongoDBService,
        private SerializerInterface $serializer
    ) {
    }

    /**
     * PHYD - Getting Driving Score Data
     *
     * @param string $vin The Vehicle Identification Number (VIN) to filter by.
     * @param string|null $stliPolicyNumber (Optional) The STLI Policy Number to include in the filter.
     */
    public function getDrivingScore(string $vin, ?string $stliPolicyNumber = null): WSResponse
    {
        $this->logger->info('Getting driving score', [
            'vin' => $vin,
            'stliPolicyNumber' => $stliPolicyNumber,
        ]);

        try {
            $repository = $this->mongoDBService->getDocumentManager()->getRepository(DrivingScore::class);
            $queryBuilder = $repository->createQueryBuilder()
                ->field('vin')->equals($vin);

            if (!empty($stliPolicyNumber)) {
                $queryBuilder->field('stliPolicyNumber')->equals($stliPolicyNumber);
            }

            $drivingScores = $queryBuilder->getQuery()->execute()->toArray();

            // Convert to array format with rich structure
            $result = [];
            foreach ($drivingScores as $drivingScore) {
                $result[] = [
                    '_id' => $drivingScore->getId(),
                    'vin' => $drivingScore->getVin(),
                    'updateDate' => $drivingScore->getUpdateDate(),
                    'overallScore' => $drivingScore->getOverallScore(),
                    'dailyScore' => $drivingScore->getDailyScore(),
                    'isValid' => $drivingScore->getIsValid(),
                    'stliPolicyNumber' => $drivingScore->getStliPolicyNumber(),
                    'contractNumber' => $drivingScore->getContractNumber(),
                    'validFlag' => $drivingScore->getValidFlag(),
                    'globalScore' => $drivingScore->getGlobalScore(),
                    'accelerationScore' => $drivingScore->getAccelerationScore(),
                    'brakingScore' => $drivingScore->getBrakingScore(),
                    'lastUpdate' => $drivingScore->getLastUpdate()?->format('Y-m-d H:i:s')
                ];
            }

            // Return in the expected format with documents array
            $responseData = [
                'documents' => $result
            ];

            return new WSResponse(200, json_encode($responseData));
        } catch (\Exception $e) {
            $this->logger->error('Error getting driving score', [
                'vin' => $vin,
                'stliPolicyNumber' => $stliPolicyNumber,
                'error' => $e->getMessage()
            ]);
            return new WSResponse(500, json_encode(['error' => $e->getMessage()]));
        }
    }
}
