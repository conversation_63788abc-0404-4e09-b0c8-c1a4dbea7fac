<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\Dealer;
use Space\MongoDocuments\Repository\DealerRepository;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\HttpFoundation\Response;

/**
 * Enhanced Dealer Service using MongoDB ODM
 */
class EnhancedDealerService
{
    use LoggerTrait;

    private DealerRepository $dealerRepository;

    public function __construct(
        private DocumentManager $documentManager,
        private DealerService $fallbackService
    ) {
        $this->dealerRepository = $this->documentManager->getRepository(Dealer::class);
    }

    /**
     * Get dealer list with enhanced MongoDB integration
     */
    public function getDealerList(array $params): WSResponse
    {
        try {
            $this->logger->info('=> ' . __METHOD__ . ' => Enhanced dealer search with params', $params);

            // Extract search parameters
            $brand = $params['brand'] ?? '';
            $country = $params['countryApv'] ?: $params['country'] ?? '';
            $latitude = (float) ($params['latitude'] ?? 0);
            $longitude = (float) ($params['longitude'] ?? 0);
            $maxDistance = (int) ($params['rmax'] ?? 50);
            $resultMax = (int) ($params['resultmax'] ?? 20);
            $criterias = $params['criterias'] ?? '';

            // Validate required parameters
            if (empty($brand) || empty($country)) {
                return new WSResponse(
                    Response::HTTP_BAD_REQUEST,
                    ['error' => ['message' => 'Brand and country are required parameters']]
                );
            }

            // Search dealers using MongoDB
            $dealers = $this->searchDealers($brand, $country, $latitude, $longitude, $maxDistance, $criterias);

            if (empty($dealers)) {
                $this->logger->warning('=> ' . __METHOD__ . ' => No dealers found, falling back to external API');
                return $this->fallbackService->getDealerList($params);
            }

            // Limit results
            $dealers = array_slice($dealers, 0, $resultMax);

            // Convert to expected format
            $dealerData = $this->convertDealersToApiFormat($dealers);

            $this->logger->info('=> ' . __METHOD__ . ' => Found ' . count($dealers) . ' dealers from MongoDB');

            return new WSResponse(Response::HTTP_OK, ['success' => ['Dealers' => $dealerData]]);

        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error in enhanced dealer search: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'params' => $params
            ]);

            // Fallback to original service on error
            return $this->fallbackService->getDealerList($params);
        }
    }

    /**
     * Search dealers with geographic and business criteria
     */
    private function searchDealers(
        string $brand,
        string $country,
        float $latitude,
        float $longitude,
        int $maxDistance,
        string $criterias
    ): array {
        try {
            // Start with active dealers for the brand and country
            $dealers = $this->dealerRepository->findBy([
                'brand' => $brand,
                'status' => ['$in' => ['active', 'enabled']],
                'addressData.country' => $country
            ]);

            // Filter by geographic proximity if coordinates provided
            if ($latitude && $longitude) {
                $nearbyDealers = [];
                foreach ($dealers as $dealer) {
                    $distance = $dealer->distanceToPoint($latitude, $longitude);
                    if ($distance !== null && $distance <= $maxDistance) {
                        $nearbyDealers[] = [
                            'dealer' => $dealer,
                            'distance' => $distance
                        ];
                    }
                }

                // Sort by distance
                usort($nearbyDealers, fn($a, $b) => $a['distance'] <=> $b['distance']);
                $dealers = array_column($nearbyDealers, 'dealer');
            }

            // Apply business criteria filters
            if (!empty($criterias)) {
                $dealers = $this->filterByCriterias($dealers, $criterias);
            }

            return $dealers;

        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error searching dealers: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Filter dealers by business criterias
     */
    private function filterByCriterias(array $dealers, string $criterias): array
    {
        if (empty($criterias)) {
            return $dealers;
        }

        $criteriaList = explode(',', $criterias);
        $filteredDealers = [];

        foreach ($dealers as $dealer) {
            $matchesCriteria = true;

            foreach ($criteriaList as $criteria) {
                $criteria = trim($criteria);
                if (!$this->dealerMatchesCriteria($dealer, $criteria)) {
                    $matchesCriteria = false;
                    break;
                }
            }

            if ($matchesCriteria) {
                $filteredDealers[] = $dealer;
            }
        }

        return $filteredDealers;
    }

    /**
     * Check if dealer matches specific criteria
     */
    private function dealerMatchesCriteria(Dealer $dealer, string $criteria): bool
    {
        $business = $dealer->getBusiness();
        if (!$business) {
            return false;
        }

        // Check business indicators
        return $business->hasIndicator($criteria);
    }

    /**
     * Convert Dealer documents to API format
     */
    private function convertDealersToApiFormat(array $dealers): array
    {
        $apiDealers = [];

        foreach ($dealers as $dealer) {
            try {
                $apiDealer = $this->convertDealerToApiFormat($dealer);
                if ($apiDealer) {
                    $apiDealers[] = $apiDealer;
                }
            } catch (\Exception $e) {
                $this->logger->error('=> ' . __METHOD__ . ' => Error converting dealer: ' . $e->getMessage(), [
                    'dealerId' => $dealer->getDealerId()
                ]);
                // Continue with other dealers
            }
        }

        return $apiDealers;
    }

    /**
     * Convert single Dealer document to API format
     */
    private function convertDealerToApiFormat(Dealer $dealer): ?array
    {
        try {
            // Ensure data is synced to arrays for backward compatibility
            $dealer->syncToArrays();

            $address = $dealer->getAddress();
            $coordinate = $dealer->getCoordinate();
            $contact = $dealer->getContact();
            $business = $dealer->getBusiness();
            $website = $dealer->getWebsite();
            $codes = $dealer->getCodes();

            return [
                'SiteGeo' => $dealer->getDealerId(),
                'RRDI' => $codes?->getDealerCode() ?? '',
                'Name' => $dealer->getName(),
                'Brand' => $dealer->getBrand(),
                'CountryId' => $address?->getCountry() ?? '',
                'Culture' => '', // Will be set by transformer
                'DistanceFromPoint' => 0, // Will be calculated if needed
                'IsAgent' => false, // Default values
                'IsAgentAP' => false,
                'IsSecondary' => false,
                'IsSuccursale' => false,
                'Address' => [
                    'Line1' => $address?->getLine1() ?? '',
                    'Line2' => $address?->getLine2() ?? '',
                    'Line3' => $address?->getLine3() ?? '',
                    'City' => $address?->getCity() ?? '',
                    'Region' => $address?->getRegion() ?? '',
                    'Country' => $address?->getCountry() ?? '',
                    'ZipCode' => $address?->getZipCode() ?? '',
                ],
                'Coordinates' => [
                    'Latitude' => $coordinate?->getLatitude() ?? 0,
                    'Longitude' => $coordinate?->getLongitude() ?? 0,
                ],
                'Phones' => [
                    'PhoneNumber' => $contact?->getPhoneNumber() ?? '',
                    'PhoneAPV' => $contact?->getPhoneApv() ?? '',
                    'PhonePR' => $contact?->getPhonePr() ?? '',
                    'PhoneVN' => $contact?->getPhoneVn() ?? '',
                    'PhoneVO' => $contact?->getPhoneVo() ?? '',
                ],
                'Emails' => [
                    'Email' => $contact?->getEmail() ?? '',
                    'EmailAPV' => $contact?->getEmailApv() ?? '',
                    'EmailAgent' => $contact?->getEmailAgent() ?? '',
                    'EmailGER' => $contact?->getEmailGer() ?? '',
                    'EmailGRC' => $contact?->getEmailGrc() ?? '',
                    'EmailPR' => $contact?->getEmailPr() ?? '',
                    'EmailSales' => $contact?->getEmailSales() ?? '',
                    'EmailVO' => $contact?->getEmailVo() ?? '',
                ],
                'BusinessList' => $this->convertBusinessList($business),
                'OpeningHoursList' => $this->convertOpeningHours($business),
                'WebSites' => [
                    'Public' => $website?->getPrimaryUrl() ?? '',
                    'Private' => '',
                ],
                'UrlPages' => [
                    'UrlAPVForm' => $website?->getServiceUrl() ?? '',
                    'UrlContact' => '',
                    'UrlNewCarStock' => $website?->getNewVehiclesUrl() ?? '',
                    'UrlUsedCarStock' => $website?->getUsedVehiclesUrl() ?? '',
                    'UrlUsefullInformation' => '',
                ],
                'Principal' => [
                    'IsPrincipalAG' => false,
                    'IsPrincipalPR' => false,
                    'IsPrincipalRA' => false,
                    'IsPrincipalVN' => false,
                    'IsPrincipalVO' => false,
                ],
                // Additional fields for compatibility
                'BenefitList' => [],
                'CodesActors' => $this->convertCodesActors($codes),
                'CodesRegions' => $this->convertCodesRegions($codes),
                'FaxNumber' => '',
                'Group' => [
                    'GroupId' => '',
                    'IsLeader' => false,
                    'SubGroupId' => '',
                    'SubGrouplabel' => '',
                ],
                'Indicator' => [
                    'Code' => '',
                    'label' => '',
                ],
                'WelcomeMessage' => '',
                'Importer' => $this->getDefaultImporter(),
                'PDVImporter' => $this->getDefaultPDVImporter(),
                'NumSiret' => '',
                'LegalStatus' => '',
                'Capital' => '',
                'CommercialRegister' => '',
                'IntracommunityTVA' => '',
                'ParentSiteGeo' => '',
                'RaisonSocial' => '',
                'RCSNumber' => '',
                'GmCodeList' => [],
                'LienVoList' => [],
                'bqCaptive' => '',
                'carac_rdvi' => '',
                'FtcCodeList' => [],
                'AdrLivVNList' => [],
                'ContratVl' => $this->getDefaultContratVl(),
            ];

        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error converting dealer to API format: ' . $e->getMessage(), [
                'dealerId' => $dealer->getDealerId()
            ]);
            return null;
        }
    }

    /**
     * Convert business data to API format
     */
    private function convertBusinessList($business): array
    {
        if (!$business) {
            return [];
        }

        $businessList = [];
        $indicators = $business->getIndicators();

        foreach ($indicators as $indicator) {
            $businessList[] = [
                'Code' => $indicator['code'] ?? '',
                'Label' => $indicator['label'] ?? '',
                'Type' => 'service', // Default type
            ];
        }

        return $businessList;
    }

    /**
     * Convert opening hours to API format
     */
    private function convertOpeningHours($business): array
    {
        if (!$business) {
            return [];
        }

        $openingHoursList = [];
        $hours = $business->getOpeningHours();

        foreach ($hours as $hour) {
            $openingHoursList[] = [
                'Label' => $hour['day'] ?? '',
                'Type' => $hour['openTime'] . ' - ' . $hour['closeTime'],
            ];
        }

        return $openingHoursList;
    }

    /**
     * Convert codes actors to API format
     */
    private function convertCodesActors($codes): array
    {
        return [
            'CodeActorAddressPR' => '',
            'CodeActorAddressRA' => '',
            'CodeActorAddressVN' => '',
            'CodeActorAddressVO' => '',
            'CodeActorCC_AG' => '',
            'CodeActorCC_PR' => '',
            'CodeActorCC_RA' => '',
            'CodeActorCC_VN' => '',
            'CodeActorCC_VO' => '',
            'CodeActorSearch' => $codes?->getCodeActeur() ?? '',
        ];
    }

    /**
     * Convert codes regions to API format
     */
    private function convertCodesRegions($codes): array
    {
        return [
            'CodeRegionAG' => '',
            'CodeRegionPR' => '',
            'CodeRegionRA' => '',
            'CodeRegionVN' => '',
            'CodeRegionVO' => '',
        ];
    }

    /**
     * Get default importer data
     */
    private function getDefaultImporter(): array
    {
        return [
            'ImporterCode' => '',
            'CorporateName' => '',
            'ImporterName' => '',
            'Address' => '',
            'City' => '',
            'ManagementCountry' => '',
            'Country' => '',
            'Subsidiary' => '',
            'SubsidiaryName' => '',
        ];
    }

    /**
     * Get default PDV importer data
     */
    private function getDefaultPDVImporter(): array
    {
        return [
            'PDVCode' => '',
            'PDVName' => '',
            'PDVContact' => '',
        ];
    }

    /**
     * Get default contract VL data
     */
    private function getDefaultContratVl(): array
    {
        return [
            'CodeActorAddressVL' => '',
            'CodeActorCC_VL' => '',
            'CodeRegionVL' => '',
            'EmailVL' => '',
            'PhoneVL' => '',
            'IsPrincipalVL' => false,
        ];
    }

    /**
     * Get dealer by ID with enhanced error handling
     */
    public function getDealerById(string $dealerId): ?Dealer
    {
        try {
            return $this->dealerRepository->findByDealerId($dealerId);
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error getting dealer by ID: ' . $e->getMessage(), [
                'dealerId' => $dealerId
            ]);
            return null;
        }
    }

    /**
     * Get dealers near location with enhanced error handling
     */
    public function getDealersNearLocation(float $latitude, float $longitude, float $maxDistance = 50): array
    {
        try {
            return $this->dealerRepository->findNearLocation($latitude, $longitude, $maxDistance);
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error getting dealers near location: ' . $e->getMessage(), [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'maxDistance' => $maxDistance
            ]);
            return [];
        }
    }

    /**
     * Validate dealer data completeness
     */
    public function validateDealerData(Dealer $dealer): array
    {
        $errors = [];

        if (!$dealer->getName()) {
            $errors[] = 'Dealer name is required';
        }

        if (!$dealer->getDealerId()) {
            $errors[] = 'Dealer ID is required';
        }

        if (!$dealer->getAddress() || !$dealer->getAddress()->isComplete()) {
            $errors[] = 'Complete address information is required';
        }

        if (!$dealer->getCoordinate() || !$dealer->getCoordinate()->isValid()) {
            $errors[] = 'Valid coordinates are required';
        }

        if (!$dealer->getContact() || !$dealer->getContact()->hasContactInfo()) {
            $errors[] = 'Contact information is required';
        }

        return $errors;
    }

    /**
     * Get dealer statistics for monitoring
     */
    public function getDealerStatistics(): array
    {
        try {
            return $this->dealerRepository->getStatistics();
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error getting dealer statistics: ' . $e->getMessage());
            return [
                'total' => 0,
                'active' => 0,
                'inactive' => 0,
                'byBrand' => [],
                'byMarket' => [],
                'error' => $e->getMessage()
            ];
        }
    }
}
