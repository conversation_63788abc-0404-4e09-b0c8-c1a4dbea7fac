<?php

namespace App\Controller;

use App\Manager\UserManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\DrivingScore;

#[Route('/test', name: 'test_')]
class TestController extends AbstractController
{
    #[Route('/user-vehicles/{userId}', name: 'user_vehicles', methods: ['GET'])]
    public function getUserVehicles(string $userId, UserManager $userManager): JsonResponse
    {
        try {
            $userData = $userManager->getUserByUserId($userId);

            if ($userData->getCode() !== 200) {
                return $this->json([
                    'error' => 'User not found',
                    'code' => $userData->getCode()
                ], $userData->getCode());
            }

            $rawData = $userData->getData();
            $decodedData = json_decode($rawData, true);
            $userData = $decodedData['documents'][0] ?? null;

            if (!$userData) {
                return $this->json(['error' => 'No user data found'], 404);
            }

            $vehicles = $userData['vehicles'] ?? [];

            return $this->json([
                'userId' => $userId,
                'vehicleCount' => count($vehicles),
                'vehicles' => $vehicles
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/driving-scores', name: 'driving_scores', methods: ['GET'])]
    public function getDrivingScores(Request $request, MongoDBService $mongoDBService): JsonResponse
    {
        try {
            $vin = $request->query->get('vin');

            $repository = $mongoDBService->getDocumentManager()->getRepository(DrivingScore::class);

            if ($vin) {
                $drivingScores = $repository->findBy(['vin' => $vin]);
            } else {
                // Get all driving scores (limit to 10 for testing)
                $drivingScores = $repository->findBy([], null, 10);
            }

            $result = [];
            foreach ($drivingScores as $drivingScore) {
                $result[] = [
                    'id' => $drivingScore->getId(),
                    'vin' => $drivingScore->getVin(),
                    'contractNumber' => $drivingScore->getContractNumber(),
                    'stliPolicyNumber' => $drivingScore->getStliPolicyNumber(),
                    'validFlag' => $drivingScore->getValidFlag(),
                    'isValid' => $drivingScore->getIsValid(),
                    'globalScore' => $drivingScore->getGlobalScore(),
                    'accelerationScore' => $drivingScore->getAccelerationScore(),
                    'brakingScore' => $drivingScore->getBrakingScore(),
                    'lastUpdate' => $drivingScore->getLastUpdate()?->format('Y-m-d H:i:s'),
                ];
            }

            return $this->json([
                'count' => count($result),
                'drivingScores' => $result
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/create-test-driving-score', name: 'create_test_driving_score', methods: ['POST'])]
    public function createTestDrivingScore(Request $request, MongoDBService $mongoDBService): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $vin = $data['vin'] ?? 'VR3UPHNKSKT101603'; // Use one of the user's VINs

            $drivingScore = new DrivingScore();
            $drivingScore->setVin($vin);
            $drivingScore->setContractNumber('TEST123456');
            $drivingScore->setStliPolicyNumber('CN123321');
            $drivingScore->setValidFlag('Y');
            $drivingScore->setIsValid(true);
            $drivingScore->setGlobalScore(85);
            $drivingScore->setAccelerationScore(90);
            $drivingScore->setBrakingScore(80);
            $drivingScore->setLastUpdate(new \DateTime());
            $drivingScore->setUpdateDate('2024-12-10');

            // Set rich score structure with realistic data
            $drivingScore->setOverallScore([
                'value' => rand(30, 95) + (rand(0, 99) / 100) // Random score between 30-95
            ]);

            $drivingScore->setDailyScore([
                'value' => rand(40, 90) + (rand(0, 99) / 100), // Random daily score
                'subScore' => [
                    'dynamincs' => [
                        'percentageOfGood' => rand(30, 80) + (rand(0, 99) / 100),
                        'percentageOfAverage' => rand(10, 30) + (rand(0, 99) / 100),
                        'percentageOfBad' => rand(5, 25) + (rand(0, 99) / 100),
                        'tips' => 'Maintain smooth acceleration patterns'
                    ],
                    'decelaration' => [
                        'percentageOfGood' => rand(50, 85) + (rand(0, 99) / 100),
                        'percentageOfAverage' => rand(5, 20) + (rand(0, 99) / 100),
                        'percentageOfBad' => rand(10, 30) + (rand(0, 99) / 100),
                        'tips' => 'Gradual braking improves safety'
                    ],
                    'cornering' => [
                        'percentageOfGood' => rand(40, 75) + (rand(0, 99) / 100),
                        'percentageOfAverage' => rand(15, 35) + (rand(0, 99) / 100),
                        'percentageOfBad' => rand(5, 20) + (rand(0, 99) / 100),
                        'tips' => 'Take corners at appropriate speeds'
                    ]
                ]
            ]);

            $mongoDBService->save($drivingScore);

            return $this->json([
                'message' => 'Test driving score created successfully',
                'vin' => $vin,
                'id' => $drivingScore->getId()
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/test-driving-score-activation', name: 'test_driving_score_activation', methods: ['POST'])]
    public function testDrivingScoreActivation(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $userId = $data['userId'] ?? '6f5853d0f9e2442c9c818fecf0eec7cf';
            $vin = $data['vin'] ?? 'VR3UPHNKSKT101603';
            $stliPolicyNumber = $data['stliPolicyNumber'] ?? 'TEST123456';

            // Make internal request to the actual activation endpoint
            $activationData = [
                'vin' => $vin,
                'stliPolicyNumber' => $stliPolicyNumber
            ];

            $client = new \GuzzleHttp\Client();
            $response = $client->post('http://127.0.0.1:8000/v1/vehicle/driving-score/activation', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'userId' => $userId
                ],
                'json' => $activationData
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            return $this->json([
                'status' => 'success',
                'activation_response' => $responseData,
                'test_data' => [
                    'userId' => $userId,
                    'vin' => $vin,
                    'stliPolicyNumber' => $stliPolicyNumber
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    #[Route('/test-subscription', name: 'test_subscription', methods: ['GET'])]
    public function testSubscription(Request $request): JsonResponse
    {
        try {
            $userId = $request->query->get('userId', '6f5853d0f9e2442c9c818fecf0eec7cf');
            $vin = $request->query->get('vin', 'VR3UPHNKSKT101603');
            $target = $request->query->get('target', 'test');

            // Make internal request to the subscription endpoint
            $client = new \GuzzleHttp\Client();
            $response = $client->get('http://127.0.0.1:8000/v1/subscription', [
                'headers' => [
                    'userId' => $userId,
                    'vin' => $vin
                ],
                'query' => [
                    'target' => $target
                ]
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            return $this->json([
                'status' => 'success',
                'subscription_response' => $responseData,
                'test_data' => [
                    'userId' => $userId,
                    'vin' => $vin,
                    'target' => $target
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }


}
