<?php

namespace App\Exception;

use Exception;

/**
 * Custom exception for dealer-related errors
 */
class DealerException extends Exception
{
    public const DATABASE_CONNECTION_ERROR = 1001;
    public const DEALER_NOT_FOUND = 1002;
    public const INVALID_DEALER_DATA = 1003;
    public const GEOGRAPHIC_CALCULATION_ERROR = 1004;
    public const MISSING_DEALER_INFO = 1005;
    public const API_RESPONSE_FORMAT_ERROR = 1006;
    public const DEALER_VALIDATION_ERROR = 1007;
    public const DEALER_SEARCH_ERROR = 1008;
    public const DEALER_CONVERSION_ERROR = 1009;
    public const DEALER_REPOSITORY_ERROR = 1010;

    private array $context = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        ?Exception $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    public function addContext(string $key, $value): self
    {
        $this->context[$key] = $value;
        return $this;
    }

    /**
     * Create database connection error
     */
    public static function databaseConnectionError(string $message = 'Database connection failed', array $context = []): self
    {
        return new self($message, self::DATABASE_CONNECTION_ERROR, null, $context);
    }

    /**
     * Create dealer not found error
     */
    public static function dealerNotFound(string $dealerId = '', array $context = []): self
    {
        $message = empty($dealerId) ? 'Dealer not found' : "Dealer not found: {$dealerId}";
        return new self($message, self::DEALER_NOT_FOUND, null, array_merge($context, ['dealerId' => $dealerId]));
    }

    /**
     * Create invalid dealer data error
     */
    public static function invalidDealerData(string $message = 'Invalid dealer data', array $context = []): self
    {
        return new self($message, self::INVALID_DEALER_DATA, null, $context);
    }

    /**
     * Create geographic calculation error
     */
    public static function geographicCalculationError(string $message = 'Geographic calculation failed', array $context = []): self
    {
        return new self($message, self::GEOGRAPHIC_CALCULATION_ERROR, null, $context);
    }

    /**
     * Create missing dealer information error
     */
    public static function missingDealerInfo(array $missingFields = [], array $context = []): self
    {
        $message = empty($missingFields) 
            ? 'Missing dealer information' 
            : 'Missing dealer information: ' . implode(', ', $missingFields);
        return new self($message, self::MISSING_DEALER_INFO, null, array_merge($context, ['missingFields' => $missingFields]));
    }

    /**
     * Create API response format error
     */
    public static function apiResponseFormatError(string $message = 'API response format error', array $context = []): self
    {
        return new self($message, self::API_RESPONSE_FORMAT_ERROR, null, $context);
    }

    /**
     * Create dealer validation error
     */
    public static function dealerValidationError(array $validationErrors = [], array $context = []): self
    {
        $message = empty($validationErrors) 
            ? 'Dealer validation failed' 
            : 'Dealer validation failed: ' . implode(', ', $validationErrors);
        return new self($message, self::DEALER_VALIDATION_ERROR, null, array_merge($context, ['validationErrors' => $validationErrors]));
    }

    /**
     * Create dealer search error
     */
    public static function dealerSearchError(string $message = 'Dealer search failed', array $context = []): self
    {
        return new self($message, self::DEALER_SEARCH_ERROR, null, $context);
    }

    /**
     * Create dealer conversion error
     */
    public static function dealerConversionError(string $message = 'Dealer data conversion failed', array $context = []): self
    {
        return new self($message, self::DEALER_CONVERSION_ERROR, null, $context);
    }

    /**
     * Create dealer repository error
     */
    public static function dealerRepositoryError(string $message = 'Dealer repository error', array $context = []): self
    {
        return new self($message, self::DEALER_REPOSITORY_ERROR, null, $context);
    }

    /**
     * Get error type name
     */
    public function getErrorType(): string
    {
        return match ($this->getCode()) {
            self::DATABASE_CONNECTION_ERROR => 'DATABASE_CONNECTION_ERROR',
            self::DEALER_NOT_FOUND => 'DEALER_NOT_FOUND',
            self::INVALID_DEALER_DATA => 'INVALID_DEALER_DATA',
            self::GEOGRAPHIC_CALCULATION_ERROR => 'GEOGRAPHIC_CALCULATION_ERROR',
            self::MISSING_DEALER_INFO => 'MISSING_DEALER_INFO',
            self::API_RESPONSE_FORMAT_ERROR => 'API_RESPONSE_FORMAT_ERROR',
            self::DEALER_VALIDATION_ERROR => 'DEALER_VALIDATION_ERROR',
            self::DEALER_SEARCH_ERROR => 'DEALER_SEARCH_ERROR',
            self::DEALER_CONVERSION_ERROR => 'DEALER_CONVERSION_ERROR',
            self::DEALER_REPOSITORY_ERROR => 'DEALER_REPOSITORY_ERROR',
            default => 'UNKNOWN_ERROR',
        };
    }

    /**
     * Check if error is recoverable (can fallback to external API)
     */
    public function isRecoverable(): bool
    {
        return in_array($this->getCode(), [
            self::DATABASE_CONNECTION_ERROR,
            self::DEALER_NOT_FOUND,
            self::MISSING_DEALER_INFO,
            self::DEALER_VALIDATION_ERROR,
            self::DEALER_REPOSITORY_ERROR,
        ]);
    }

    /**
     * Get error details for logging
     */
    public function getErrorDetails(): array
    {
        return [
            'errorType' => $this->getErrorType(),
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'context' => $this->getContext(),
            'isRecoverable' => $this->isRecoverable(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
        ];
    }

    /**
     * Convert to array for API response
     */
    public function toArray(): array
    {
        return [
            'error' => [
                'type' => $this->getErrorType(),
                'message' => $this->getMessage(),
                'code' => $this->getCode(),
                'recoverable' => $this->isRecoverable(),
            ]
        ];
    }
}
