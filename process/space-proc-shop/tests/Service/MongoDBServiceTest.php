<?php

namespace App\Tests\Service;

use App\Service\MongoDBService;
use Doctrine\ODM\MongoDB\DocumentManager;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Space\MongoDocuments\Document\Settings;
use Space\MongoDocuments\Repository\SettingsRepository;

class MongoDBServiceTest extends TestCase
{
    private MongoDBService $mongoDBService;
    private DocumentManager $documentManager;
    private LoggerInterface $logger;
    private SettingsRepository $settingsRepository;

    protected function setUp(): void
    {
        $this->documentManager = $this->createMock(DocumentManager::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->settingsRepository = $this->createMock(SettingsRepository::class);
        
        $this->mongoDBService = new MongoDBService(
            $this->documentManager,
            $this->logger
        );
    }

    public function testFindSettingsByFilterWithComplexFilter(): void
    {
        // Test data
        $filter = [
            'brand' => 'DS',
            'source' => 'APP',
            'culture' => '',
            '$or' => [
                ["settingsData.o2x.code" => "o2x"],
                ["settingsData.config.code" => "o2x"]
            ]
        ];

        $expectedSettings = new Settings();
        $expectedSettings->setBrand('DS');
        $expectedSettings->setSource('APP');
        $expectedSettings->setSettingsData(['o2x' => ['enabled' => true]]);

        // Mock repository behavior
        $this->settingsRepository
            ->expects($this->once())
            ->method('findByComplexFilter')
            ->with($filter)
            ->willReturn($expectedSettings);

        $this->documentManager
            ->expects($this->once())
            ->method('getRepository')
            ->with(Settings::class)
            ->willReturn($this->settingsRepository);

        // Execute
        $result = $this->mongoDBService->findSettingsByFilter($filter);

        // Assert
        $this->assertInstanceOf(Settings::class, $result);
        $this->assertEquals('DS', $result->getBrand());
        $this->assertEquals('APP', $result->getSource());
    }

    public function testFindSettingsByFilterWithBasicFilter(): void
    {
        // Test data
        $filter = [
            'brand' => 'DS',
            'source' => 'WEB',
            'culture' => 'en'
        ];

        $expectedSettings = new Settings();
        $expectedSettings->setBrand('DS');
        $expectedSettings->setSource('WEB');

        // Mock repository without findByComplexFilter method
        $basicRepository = $this->createMock(\Doctrine\ODM\MongoDB\Repository\DocumentRepository::class);
        $basicRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with([
                'brand' => 'DS',
                'source' => 'WEB',
                'culture' => 'en'
            ])
            ->willReturn($expectedSettings);

        $this->documentManager
            ->expects($this->once())
            ->method('getRepository')
            ->with(Settings::class)
            ->willReturn($basicRepository);

        // Execute
        $result = $this->mongoDBService->findSettingsByFilter($filter);

        // Assert
        $this->assertInstanceOf(Settings::class, $result);
        $this->assertEquals('DS', $result->getBrand());
        $this->assertEquals('WEB', $result->getSource());
    }

    public function testFindSettingsByFilterHandlesException(): void
    {
        // Test data
        $filter = ['brand' => 'DS'];

        // Mock repository to throw exception
        $this->documentManager
            ->expects($this->once())
            ->method('getRepository')
            ->with(Settings::class)
            ->willThrowException(new \Exception('Database error'));

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with(
                'Error finding settings by filter',
                $this->callback(function ($context) {
                    return isset($context['filter']) && isset($context['error']);
                })
            );

        // Execute
        $result = $this->mongoDBService->findSettingsByFilter($filter);

        // Assert
        $this->assertNull($result);
    }
}
