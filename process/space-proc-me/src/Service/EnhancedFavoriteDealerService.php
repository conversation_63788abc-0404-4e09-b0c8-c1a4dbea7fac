<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\Dealer;
use Space\MongoDocuments\Repository\DealerRepository;
use Doctrine\ODM\MongoDB\DocumentManager;
use Symfony\Component\HttpFoundation\Response;

/**
 * Enhanced Favorite Dealer Service using MongoDB ODM
 */
class EnhancedFavoriteDealerService
{
    use LoggerTrait;

    private DealerRepository $dealerRepository;

    public function __construct(
        private DocumentManager $documentManager,
        private FavoriteDealerService $fallbackService
    ) {
        $this->dealerRepository = $this->documentManager->getRepository(Dealer::class);
    }

    /**
     * Get XP dealer details with enhanced MongoDB integration
     */
    public function getXpDealerDetails(array $params): WSResponse
    {
        try {
            $this->logger->info('=> ' . __METHOD__ . ' => Enhanced XP dealer details request', $params);

            $siteGeo = $params['siteGeo'] ?? '';
            $brand = $params['brand'] ?? '';
            $country = $params['country'] ?? '';

            if (empty($siteGeo)) {
                return new WSResponse(Response::HTTP_NOT_FOUND, ['error' => 'No favorite dealer found']);
            }

            // Try to find dealer in MongoDB first
            $dealer = $this->dealerRepository->findByDealerId($siteGeo);

            if (!$dealer) {
                $this->logger->warning('=> ' . __METHOD__ . ' => Dealer not found in MongoDB, falling back to external API');
                return $this->fallbackService->getXpDealerDetails($params);
            }

            // Validate dealer data
            $validationErrors = $this->validateDealerData($dealer);
            if (!empty($validationErrors)) {
                $this->logger->warning('=> ' . __METHOD__ . ' => Dealer data validation failed, falling back to external API', [
                    'dealerId' => $siteGeo,
                    'errors' => $validationErrors
                ]);
                return $this->fallbackService->getXpDealerDetails($params);
            }

            // Check if dealer matches brand and country
            if (!$this->dealerMatchesCriteria($dealer, $brand, $country)) {
                $this->logger->warning('=> ' . __METHOD__ . ' => Dealer does not match criteria, falling back to external API', [
                    'dealerId' => $siteGeo,
                    'dealerBrand' => $dealer->getBrand(),
                    'dealerCountry' => $dealer->getAddress()?->getCountry(),
                    'requestedBrand' => $brand,
                    'requestedCountry' => $country
                ]);
                return $this->fallbackService->getXpDealerDetails($params);
            }

            // Convert dealer to API format
            $dealerData = $this->convertDealerToXpApiFormat($dealer);

            $this->logger->info('=> ' . __METHOD__ . ' => Successfully retrieved dealer from MongoDB', [
                'dealerId' => $siteGeo
            ]);

            return new WSResponse(Response::HTTP_OK, ['success' => [$dealerData]]);

        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error in enhanced XP dealer details: ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e->getMessage()
            ]);

            // Fallback to original service on error
            return $this->fallbackService->getXpDealerDetails($params);
        }
    }

    /**
     * Get XF dealer details with enhanced MongoDB integration
     */
    public function getXfDealerDetails(array $params): WSResponse
    {
        try {
            $this->logger->info('=> ' . __METHOD__ . ' => Enhanced XF dealer details request', $params);

            $siteGeo = $params['siteGeo'] ?? '';
            $brand = $params['brand'] ?? '';

            if (empty($siteGeo)) {
                return new WSResponse(Response::HTTP_NOT_FOUND, ['error' => 'No favorite dealer found']);
            }

            // Parse dealer IDs from siteGeo (format: "dealerId|sincom")
            $dealerIds = explode('|', $siteGeo);
            $dealerId = $dealerIds[0] ?? '';

            if (empty($dealerId)) {
                $this->logger->warning('=> ' . __METHOD__ . ' => Invalid siteGeo format, falling back to external API');
                return $this->fallbackService->getXfDealerDetails($params);
            }

            // Try to find dealer in MongoDB first
            $dealer = $this->dealerRepository->findByDealerId($dealerId);

            if (!$dealer) {
                $this->logger->warning('=> ' . __METHOD__ . ' => Dealer not found in MongoDB, falling back to external API');
                return $this->fallbackService->getXfDealerDetails($params);
            }

            // Validate dealer data
            $validationErrors = $this->validateDealerData($dealer);
            if (!empty($validationErrors)) {
                $this->logger->warning('=> ' . __METHOD__ . ' => Dealer data validation failed, falling back to external API', [
                    'dealerId' => $dealerId,
                    'errors' => $validationErrors
                ]);
                return $this->fallbackService->getXfDealerDetails($params);
            }

            // Convert dealer to XF API format
            $dealerData = $this->convertDealerToXfApiFormat($dealer, $dealerIds[1] ?? '');

            $this->logger->info('=> ' . __METHOD__ . ' => Successfully retrieved XF dealer from MongoDB', [
                'dealerId' => $dealerId
            ]);

            return new WSResponse(Response::HTTP_OK, ['success' => [$dealerData]]);

        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error in enhanced XF dealer details: ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e->getMessage()
            ]);

            // Fallback to original service on error
            return $this->fallbackService->getXfDealerDetails($params);
        }
    }

    /**
     * Validate dealer data completeness
     */
    private function validateDealerData(Dealer $dealer): array
    {
        $errors = [];

        if (!$dealer->getName()) {
            $errors[] = 'Dealer name is required';
        }

        if (!$dealer->getDealerId()) {
            $errors[] = 'Dealer ID is required';
        }

        if (!$dealer->getAddress() || !$dealer->getAddress()->isComplete()) {
            $errors[] = 'Complete address information is required';
        }

        if (!$dealer->getCoordinate() || !$dealer->getCoordinate()->isValid()) {
            $errors[] = 'Valid coordinates are required';
        }

        if (!$dealer->getContact() || !$dealer->getContact()->hasContactInfo()) {
            $errors[] = 'Contact information is required';
        }

        return $errors;
    }

    /**
     * Check if dealer matches brand and country criteria
     */
    private function dealerMatchesCriteria(Dealer $dealer, string $brand, string $country): bool
    {
        // Check brand match (case-insensitive)
        if (!empty($brand) && strcasecmp($dealer->getBrand() ?? '', $brand) !== 0) {
            return false;
        }

        // Check country match (case-insensitive)
        if (!empty($country) && strcasecmp($dealer->getAddress()?->getCountry() ?? '', $country) !== 0) {
            return false;
        }

        // Check if dealer is active
        if (!$dealer->isActive()) {
            return false;
        }

        return true;
    }

    /**
     * Convert Dealer document to XP API format
     */
    private function convertDealerToXpApiFormat(Dealer $dealer): array
    {
        try {
            // Ensure data is synced to arrays for backward compatibility
            $dealer->syncToArrays();

            $address = $dealer->getAddress();
            $coordinate = $dealer->getCoordinate();
            $contact = $dealer->getContact();
            $business = $dealer->getBusiness();
            $website = $dealer->getWebsite();
            $codes = $dealer->getCodes();

            return [
                'SiteGeo' => $dealer->getDealerId(),
                'RRDI' => $codes?->getDealerCode() ?? '',
                'Name' => $dealer->getName(),
                'Brand' => $dealer->getBrand(),
                'CountryId' => $address?->getCountry() ?? '',
                'Culture' => '', // Will be set by transformer
                'DistanceFromPoint' => 0, // Will be calculated if needed
                'IsAgent' => false, // Default values
                'IsAgentAP' => false,
                'IsSecondary' => false,
                'IsSuccursale' => false,
                'Address' => [
                    'Line1' => $address?->getLine1() ?? '',
                    'Line2' => $address?->getLine2() ?? '',
                    'Line3' => $address?->getLine3() ?? '',
                    'City' => $address?->getCity() ?? '',
                    'Region' => $address?->getRegion() ?? '',
                    'Country' => $address?->getCountry() ?? '',
                    'ZipCode' => $address?->getZipCode() ?? '',
                ],
                'Coordinates' => [
                    'Latitude' => $coordinate?->getLatitude() ?? 0,
                    'Longitude' => $coordinate?->getLongitude() ?? 0,
                ],
                'Phones' => [
                    'PhoneNumber' => $contact?->getPhoneNumber() ?? '',
                    'PhoneAPV' => $contact?->getPhoneApv() ?? '',
                    'PhonePR' => $contact?->getPhonePr() ?? '',
                    'PhoneVN' => $contact?->getPhoneVn() ?? '',
                    'PhoneVO' => $contact?->getPhoneVo() ?? '',
                ],
                'Emails' => [
                    'Email' => $contact?->getEmail() ?? '',
                    'EmailAPV' => $contact?->getEmailApv() ?? '',
                    'EmailAgent' => $contact?->getEmailAgent() ?? '',
                    'EmailGER' => $contact?->getEmailGer() ?? '',
                    'EmailGRC' => $contact?->getEmailGrc() ?? '',
                    'EmailPR' => $contact?->getEmailPr() ?? '',
                    'EmailSales' => $contact?->getEmailSales() ?? '',
                    'EmailVO' => $contact?->getEmailVo() ?? '',
                ],
                'BusinessList' => $this->convertBusinessList($business),
                'OpeningHoursList' => $this->convertOpeningHours($business),
                'WebSites' => [
                    'Public' => $website?->getPrimaryUrl() ?? '',
                    'Private' => '',
                ],
                'UrlPages' => [
                    'UrlAPVForm' => $website?->getServiceUrl() ?? '',
                    'UrlContact' => '',
                    'UrlNewCarStock' => $website?->getNewVehiclesUrl() ?? '',
                    'UrlUsedCarStock' => $website?->getUsedVehiclesUrl() ?? '',
                    'UrlUsefullInformation' => '',
                ],
                'Principal' => [
                    'IsPrincipalAG' => false,
                    'IsPrincipalPR' => false,
                    'IsPrincipalRA' => false,
                    'IsPrincipalVN' => false,
                    'IsPrincipalVO' => false,
                ],
                // Additional fields for compatibility
                'BenefitList' => [],
                'CodesActors' => $this->getDefaultCodesActors($codes),
                'CodesRegions' => $this->getDefaultCodesRegions(),
                'FaxNumber' => '',
                'Group' => $this->getDefaultGroup(),
                'Indicator' => $this->getDefaultIndicator(),
                'WelcomeMessage' => '',
                'Importer' => $this->getDefaultImporter(),
                'PDVImporter' => $this->getDefaultPDVImporter(),
                'NumSiret' => '',
                'LegalStatus' => '',
                'Capital' => '',
                'CommercialRegister' => '',
                'IntracommunityTVA' => '',
                'ParentSiteGeo' => '',
                'RaisonSocial' => '',
                'RCSNumber' => '',
                'GmCodeList' => [],
                'LienVoList' => [],
                'bqCaptive' => '',
                'carac_rdvi' => '',
                'FtcCodeList' => [],
                'AdrLivVNList' => [],
                'ContratVl' => $this->getDefaultContratVl(),
            ];

        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error converting dealer to XP API format: ' . $e->getMessage(), [
                'dealerId' => $dealer->getDealerId()
            ]);
            throw $e;
        }
    }

    /**
     * Convert Dealer document to XF API format
     */
    private function convertDealerToXfApiFormat(Dealer $dealer, string $sincom = ''): array
    {
        try {
            // Ensure data is synced to arrays for backward compatibility
            $dealer->syncToArrays();

            $address = $dealer->getAddress();
            $coordinate = $dealer->getCoordinate();
            $contact = $dealer->getContact();
            $business = $dealer->getBusiness();
            $website = $dealer->getWebsite();
            $codes = $dealer->getCodes();

            return [
                'dealerId' => $dealer->getDealerId(),
                'dealerCode' => $codes?->getDealerCode() ?? '',
                'sincom' => $sincom,
                'name' => $dealer->getName(),
                'brand' => $dealer->getBrand(),
                'market' => $dealer->getMarket(),
                'status' => $dealer->getStatus(),
                'address' => [
                    'street1' => $address?->getLine1() ?? '',
                    'street2' => $address?->getLine2() ?? '',
                    'street3' => $address?->getLine3() ?? '',
                    'city' => $address?->getCity() ?? '',
                    'region' => $address?->getRegion() ?? '',
                    'country' => $address?->getCountry() ?? '',
                    'zipCode' => $address?->getZipCode() ?? '',
                ],
                'coordinates' => [
                    'latitude' => $coordinate?->getLatitude() ?? 0,
                    'longitude' => $coordinate?->getLongitude() ?? 0,
                ],
                'contact' => [
                    'phone' => $contact?->getPhoneNumber() ?? '',
                    'email' => $contact?->getEmail() ?? '',
                    'phones' => [
                        'main' => $contact?->getPhoneNumber() ?? '',
                        'service' => $contact?->getPhoneApv() ?? '',
                        'parts' => $contact?->getPhonePr() ?? '',
                        'newVehicles' => $contact?->getPhoneVn() ?? '',
                        'usedVehicles' => $contact?->getPhoneVo() ?? '',
                    ],
                    'emails' => [
                        'main' => $contact?->getEmail() ?? '',
                        'service' => $contact?->getEmailApv() ?? '',
                        'sales' => $contact?->getEmailSales() ?? '',
                        'parts' => $contact?->getEmailPr() ?? '',
                    ],
                ],
                'business' => [
                    'type' => $business?->getBusinessType() ?? '',
                    'openingHours' => $business?->getOpeningHours() ?? [],
                    'indicators' => $business?->getIndicators() ?? [],
                ],
                'website' => [
                    'main' => $website?->getPrimaryUrl() ?? '',
                    'service' => $website?->getServiceUrl() ?? '',
                    'parts' => $website?->getPartsUrl() ?? '',
                    'newVehicles' => $website?->getNewVehiclesUrl() ?? '',
                    'usedVehicles' => $website?->getUsedVehiclesUrl() ?? '',
                ],
                'codes' => [
                    'dealerCode' => $codes?->getDealerCode() ?? '',
                    'mainDealerCode' => $codes?->getMainDealerCode() ?? '',
                    'market' => $codes?->getMarket() ?? '',
                    'codeActeur' => $codes?->getCodeActeur() ?? '',
                    'idSite' => $codes?->getIdSite() ?? '',
                ],
            ];

        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error converting dealer to XF API format: ' . $e->getMessage(), [
                'dealerId' => $dealer->getDealerId()
            ]);
            throw $e;
        }
    }

    /**
     * Convert business data to API format
     */
    private function convertBusinessList($business): array
    {
        if (!$business) {
            return [];
        }

        $businessList = [];
        $indicators = $business->getIndicators();

        foreach ($indicators as $indicator) {
            $businessList[] = [
                'Code' => $indicator['code'] ?? '',
                'Label' => $indicator['label'] ?? '',
                'Type' => 'service',
            ];
        }

        return $businessList;
    }

    /**
     * Convert opening hours to API format
     */
    private function convertOpeningHours($business): array
    {
        if (!$business) {
            return [];
        }

        $openingHoursList = [];
        $hours = $business->getOpeningHours();

        foreach ($hours as $hour) {
            $openingHoursList[] = [
                'Label' => $hour['day'] ?? '',
                'Type' => $hour['openTime'] . ' - ' . $hour['closeTime'],
            ];
        }

        return $openingHoursList;
    }

    /**
     * Get default codes actors
     */
    private function getDefaultCodesActors($codes): array
    {
        return [
            'CodeActorAddressPR' => '',
            'CodeActorAddressRA' => '',
            'CodeActorAddressVN' => '',
            'CodeActorAddressVO' => '',
            'CodeActorCC_AG' => '',
            'CodeActorCC_PR' => '',
            'CodeActorCC_RA' => '',
            'CodeActorCC_VN' => '',
            'CodeActorCC_VO' => '',
            'CodeActorSearch' => $codes?->getCodeActeur() ?? '',
        ];
    }

    /**
     * Get default codes regions
     */
    private function getDefaultCodesRegions(): array
    {
        return [
            'CodeRegionAG' => '',
            'CodeRegionPR' => '',
            'CodeRegionRA' => '',
            'CodeRegionVN' => '',
            'CodeRegionVO' => '',
        ];
    }

    /**
     * Get default group data
     */
    private function getDefaultGroup(): array
    {
        return [
            'GroupId' => '',
            'IsLeader' => false,
            'SubGroupId' => '',
            'SubGrouplabel' => '',
        ];
    }

    /**
     * Get default indicator data
     */
    private function getDefaultIndicator(): array
    {
        return [
            'Code' => '',
            'label' => '',
        ];
    }

    /**
     * Get default importer data
     */
    private function getDefaultImporter(): array
    {
        return [
            'ImporterCode' => '',
            'CorporateName' => '',
            'ImporterName' => '',
            'Address' => '',
            'City' => '',
            'ManagementCountry' => '',
            'Country' => '',
            'Subsidiary' => '',
            'SubsidiaryName' => '',
        ];
    }

    /**
     * Get default PDV importer data
     */
    private function getDefaultPDVImporter(): array
    {
        return [
            'PDVCode' => '',
            'PDVName' => '',
            'PDVContact' => '',
        ];
    }

    /**
     * Get default contract VL data
     */
    private function getDefaultContratVl(): array
    {
        return [
            'CodeActorAddressVL' => '',
            'CodeActorCC_VL' => '',
            'CodeRegionVL' => '',
            'EmailVL' => '',
            'PhoneVL' => '',
            'IsPrincipalVL' => false,
        ];
    }

    /**
     * Get dealer by ID with enhanced error handling
     */
    public function getDealerById(string $dealerId): ?Dealer
    {
        try {
            return $this->dealerRepository->findByDealerId($dealerId);
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . ' => Error getting dealer by ID: ' . $e->getMessage(), [
                'dealerId' => $dealerId
            ]);
            return null;
        }
    }
}
