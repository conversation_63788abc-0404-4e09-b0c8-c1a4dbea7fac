# MongoDB Implementation Guide for space-proc-shop and space-mongo-documents

## Overview

This document outlines the MongoDB implementation using Doctrine ODM in the space-proc-shop microservice and the shared space-mongo-documents module.

## Architecture

### 1. **space-mongo-documents** (Shared Module)
- Contains MongoDB document classes and repositories
- Provides a centralized MongoDBService for database operations
- Used as a Git submodule across all microservices

### 2. **space-proc-shop** (Microservice)
- Uses space-mongo-documents for MongoDB operations
- Has its own MongoDBService that delegates to space-mongo-documents
- Configured with Doctrine ODM

## Document Structure

### Settings Document
```php
Space\MongoDocuments\Document\Settings
```

**Fields:**
- `id`: MongoDB ObjectId
- `type`: String (configuration type)
- `brand`: String (brand identifier)
- `country`: String (country code)
- `culture`: String (culture/locale)
- `source`: String (source application: APP, WEB, etc.)
- `data`: Array (general configuration data)
- `settingsData`: Array (specific settings data with nested structures)

## Key Features Implemented

### 1. **Complex Filter Support**
The implementation supports MongoDB-style filters including:
- Basic field matching
- `$or` operators for complex queries
- Nested field queries (e.g., `settingsData.o2x.code`)

### 2. **Repository Methods**
- `findByType(string $type)`
- `findByTypeBrandAndCountry(string $type, string $brand, string $country)`
- `findByTypeAndBrand(string $type, string $brand)`
- `findByBrandAndSource(string $brand, string $source)`
- `findByComplexFilter(array $filter)` - **NEW**

### 3. **Service Methods**
- `findSettingsByFilter(array $filter)` - **NEW**
- `findSettingsByTypeBrandAndCountry(string $type, string $brand, string $country)`
- Standard CRUD operations (save, update, remove)

## Configuration

### Doctrine ODM Configuration
```yaml
# process/space-proc-shop/config/packages/doctrine_mongodb.yaml
doctrine_mongodb:
    auto_generate_proxy_classes: true
    auto_generate_hydrator_classes: true
    connections:
        default:
            server: '%env(resolve:MONGODB_URL)%'
            options: {}
    default_database: '%env(resolve:MONGODB_DB)%'
    document_managers:
        default:
            auto_mapping: true
            mappings:
                SpaceMongoDocuments:
                    dir: '%kernel.project_dir%/vendor/space/mongo-documents/src/Document'
                    prefix: 'Space\MongoDocuments\Document'
```

### Service Configuration
```yaml
# process/space-proc-shop/config/services.yaml
Space\MongoDocuments\Service\MongoDBService:
    arguments:
        $documentManager: '@doctrine_mongodb.odm.document_manager'
        $logger: '@logger'
```

## Usage Examples

### 1. **Basic Settings Retrieval**
```php
$settings = $mongoService->findSettingsByTypeBrandAndCountry('config', 'DS', 'FR');
```

### 2. **Complex Filter Query (O2X Settings)**
```php
$filter = [
    'brand' => 'DS',
    'source' => 'APP',
    'culture' => '',
    '$or' => [
        ["settingsData.o2x.code" => "o2x"],
        ["settingsData.config.code" => "o2x"]
    ]
];
$settings = $mongoService->findSettingsByFilter($filter);
$settingsData = $settings ? $settings->getSettingsData() : [];
```

## Migration from MongoDB Atlas REST API

### Before (Atlas REST API)
```php
$response = $this->mongoService->find(self::COLLECTION_SETTINGS, $filter);
$result = json_decode($response->getData(), true);
$settingsData = $result['documents'][0]['settingsData'] ?? [];
```

### After (Doctrine ODM)
```php
$settings = $this->mongoService->findSettingsByFilter($filter);
$settingsData = $settings ? $settings->getSettingsData() : [];
```

## Testing

### Unit Tests Included
1. **SettingsTest.php** - Tests document getters/setters
2. **SettingsRepositoryTest.php** - Tests repository methods
3. **MongoDBServiceTest.php** - Tests service layer

### Running Tests
```bash
# In space-proc-shop
./vendor/bin/phpunit tests/Service/MongoDBServiceTest.php

# In space-mongo-documents
./vendor/bin/phpunit tests/Document/SettingsTest.php
./vendor/bin/phpunit tests/Repository/SettingsRepositoryTest.php
```

## Error Handling

- All methods include comprehensive error handling
- Detailed logging for debugging
- Graceful fallbacks for missing methods
- Exception handling with proper error messages

## Performance Considerations

- Uses Doctrine ODM query builder for efficient queries
- Proper indexing should be configured in MongoDB
- Connection pooling handled by Doctrine ODM
- Caching can be enabled in production

## Environment Variables

```env
# MongoDB ODM
MONGODB_URL='mongodb://localhost:27017'
MONGODB_DB='space'
```

## Troubleshooting

### Common Issues
1. **Missing Method Error**: Ensure all new methods are properly implemented
2. **Document Mapping Issues**: Check Doctrine ODM configuration
3. **Connection Issues**: Verify MongoDB URL and credentials
4. **Query Performance**: Add appropriate MongoDB indexes

### Debugging
- Check logs in `var/dev.log` for detailed error information
- Use MongoDB profiler to analyze query performance
- Enable Doctrine ODM logging for query debugging
